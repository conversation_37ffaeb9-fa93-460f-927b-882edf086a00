{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Debug-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-2fe2a2bc2ca0e54999a5.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Debug-9cfd285a349259c58c1b.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Debug-d3fde24b2956efec0a26.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Debug-b34edbbc90b2c657e056.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Debug-b5e073b8403ad5711d4a.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_workflow::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_workflow-Debug-e94952abce740be46f91.json", "name": "test_e2e_workflow", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Release-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-2fe2a2bc2ca0e54999a5.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Release-13ffe49278c0e762dd1d.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Release-c31f139b190b1742a195.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Release-e945879fbb1cff49db24.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Release-605319c5cca1b00514a0.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_workflow::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_workflow-Release-25f2d052d6ea44c14ba5.json", "name": "test_e2e_workflow", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-MinSizeRel-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-2fe2a2bc2ca0e54999a5.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-MinSizeRel-677b214c2a0409482a08.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-MinSizeRel-eb39a12e60b14227d963.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-MinSizeRel-241df32dbdb7b56edcec.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-MinSizeRel-4e621f34564a323adcf0.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_workflow::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_workflow-MinSizeRel-c1a5ce440a0ec193562e.json", "name": "test_e2e_workflow", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-RelWithDebInfo-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2fe2a2bc2ca0e54999a5.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-RelWithDebInfo-edcbe06bd256f125ad00.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-RelWithDebInfo-d0fedd688238e6348cc2.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-RelWithDebInfo-c07fa17d9db527b91cff.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-RelWithDebInfo-3e5081fba6f93fb70c91.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_workflow::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_workflow-RelWithDebInfo-27d9ddaa5ba05948bd7c.json", "name": "test_e2e_workflow", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/PROJECT/HighJump/build", "source": "C:/PROJECT/HighJump"}, "version": {"major": 2, "minor": 8}}